"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import SpinnerbLoader from "@/components/ui/SpinnerbLoader";
import apiRoutes from "@/lib/constant";
import apiServiceWrapper from "@/lib/services/apiService";
import { useEffect, useState } from "react";
import { MdKeyboardArrowRight } from "react-icons/md";

const CategoriesSection = ({
  selectedCategory,
  setSelectedCategory,
  setActiveFilter,
  updateURL,
  setCurrentPage,
  onFilterChange,
  isMobile = false,
}) => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const result = await apiServiceWrapper.get(apiRoutes.CATEGORY);

        if (result.error === false && result.data) {
          // const decryptedText = decryptData(result.data);
          // const categoryList = decryptedText && JSON.parse(decryptedText);
          const categoryList = result.data;
          setCategories(categoryList ?? []);
        } else {
          throw new Error(result.message || "Failed to fetch categories");
        }
      } catch (error) {
        console.error("Category fetch error:", error);
        setError(
          error.message || "An error occurred while fetching categories"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  return (
    <div className="flex flex-col space-y-0.5 text-black/60">
      <Accordion type="single" collapsible defaultValue="filter-price">
        <AccordionItem value="filter-price" className="border-none">
          <AccordionTrigger className="text-black font-bold text-xl hover:no-underline p-0 py-0.5">
            Category
          </AccordionTrigger>
          <AccordionContent
            className="pt-4"
            contentClassName="overflow-visible"
          >
            {loading ? (
              <SpinnerbLoader className="w-10 border-2 border-gray-300 border-r-gray-600" />
            ) : error ? (
              <p className="text-red-500">{error}</p>
            ) : (
              categories.length > 0 &&
              categories?.map((category) => (
                <p
                  key={category.id}
                  onClick={() => {
                    const handleClick = () => {
                      setSelectedCategory(category.slug);
                      setActiveFilter(null); // Clear active filter when category is selected
                      updateURL({ categories: category.slug, page: 1 });
                      setCurrentPage(1);
                    };

                    if (isMobile && onFilterChange) {
                      onFilterChange(handleClick);
                    } else {
                      handleClick();
                    }
                  }}
                  className={`${
                    selectedCategory === category.slug ? "text-black" : ""
                  } cursor-pointer flex items-center justify-between py-2 hover:text-black transition`}
                >
                  {category.name} <MdKeyboardArrowRight />
                </p>
              ))
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default CategoriesSection;
